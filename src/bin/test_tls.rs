use backtest::config::ConfigManager;
use std::path::PathBuf;
use tracing::{error, info, warn};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("Starting TLS functionality test...");

    // 加载TLS配置
    let config_path = PathBuf::from("test_tls_config.toml");
    match ConfigManager::load_from_file(&config_path) {
        Ok(_) => info!("✓ TLS configuration loaded successfully"),
        Err(e) => {
            error!("✗ Failed to load TLS configuration: {}", e);
            return Err(e.into());
        }
    }

    // 验证配置
    match ConfigManager::validate() {
        Ok(_) => info!("✓ TLS configuration validated successfully"),
        Err(e) => {
            error!("✗ TLS configuration validation failed: {}", e);
            return Err(e.into());
        }
    }

    // 获取配置并显示TLS状态
    let config = ConfigManager::get()?;

    info!("=== TLS Configuration Status ===");
    info!(
        "HTTP TLS: {}",
        if config.http_tls.enabled {
            "ENABLED"
        } else {
            "DISABLED"
        }
    );
    if config.http_tls.enabled {
        match &config.http_tls.cert_source {
            Some(backtest::config::TlsCertSource::Files {
                cert_path,
                key_path,
            }) => {
                info!("  Certificate: {:?}", cert_path);
                info!("  Private Key: {:?}", key_path);
            }
            Some(backtest::config::TlsCertSource::SelfSigned { subject }) => {
                info!(
                    "  Self-signed certificate for: {}",
                    subject.as_deref().unwrap_or("localhost")
                );
            }
            None => warn!("  No certificate source configured"),
        }
    }

    info!(
        "WebSocket TLS: {}",
        if config.websocket_tls.enabled {
            "ENABLED"
        } else {
            "DISABLED"
        }
    );
    if config.websocket_tls.enabled {
        match &config.websocket_tls.cert_source {
            Some(backtest::config::TlsCertSource::Files {
                cert_path,
                key_path,
            }) => {
                info!("  Certificate: {:?}", cert_path);
                info!("  Private Key: {:?}", key_path);
            }
            Some(backtest::config::TlsCertSource::SelfSigned { subject }) => {
                info!(
                    "  Self-signed certificate for: {}",
                    subject.as_deref().unwrap_or("localhost")
                );
            }
            None => warn!("  No certificate source configured"),
        }
    }

    // 测试TLS配置构建
    info!("=== Testing TLS Configuration Building ===");

    if config.http_tls.enabled {
        match backtest::tls::TlsConfigBuilder::build_server_config(&config.http_tls).await {
            Ok(Some(_)) => info!("✓ HTTP TLS configuration built successfully"),
            Ok(None) => warn!("✗ HTTP TLS configuration returned None"),
            Err(e) => {
                error!("✗ Failed to build HTTP TLS configuration: {}", e);
                return Err(e.into());
            }
        }
    }

    if config.websocket_tls.enabled {
        match backtest::tls::TlsConfigBuilder::build_server_config(&config.websocket_tls).await {
            Ok(Some(_)) => info!("✓ WebSocket TLS configuration built successfully"),
            Ok(None) => warn!("✗ WebSocket TLS configuration returned None"),
            Err(e) => {
                error!("✗ Failed to build WebSocket TLS configuration: {}", e);
                return Err(e.into());
            }
        }
    }

    // 测试服务器创建（不启动）
    info!("=== Testing Server Creation ===");

    match backtest::http::HttpServer::new() {
        Ok(_) => info!("✓ HTTP server created successfully"),
        Err(e) => {
            error!("✗ Failed to create HTTP server: {}", e);
            return Err(e.into());
        }
    }

    match backtest::websocket::WebSocketServer::new() {
        Ok(_) => info!("✓ WebSocket server created successfully"),
        Err(e) => {
            error!("✗ Failed to create WebSocket server: {}", e);
            return Err(e.into());
        }
    }

    info!("=== TLS Test Summary ===");
    info!("✓ All TLS functionality tests passed!");
    info!("✓ Certificates are valid and accessible");
    info!("✓ TLS configurations can be built successfully");
    info!("✓ Servers can be created with TLS configuration");
    info!("");
    info!("To test the actual TLS connections:");
    info!("1. Start the backtest framework with: cargo run -- --config test_tls_config.toml");
    info!("2. Test HTTPS: curl -k https://localhost:8083/api/v1/health");
    info!("3. Test WSS: Use a WebSocket client to connect to wss://localhost:8082");
    info!("");
    warn!("Note: The -k flag is needed for curl because we're using self-signed certificates");

    Ok(())
}
