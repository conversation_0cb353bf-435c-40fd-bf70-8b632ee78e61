use futures::{SinkExt, StreamExt};
use serde_json::json;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::timeout;
use tokio_tungstenite::{connect_async_tls_with_config, tungstenite::protocol::Message, Connector};
use tracing::{error, info, warn};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("Starting WSS connection test with CA certificate...");

    // 测试不同的连接方式
    let wss_url = "wss://localhost:8082";

    // 方案1：使用CA证书验证
    info!("=== Testing WSS with CA certificate verification ===");
    match test_wss_with_ca(wss_url).await {
        Ok(_) => {
            info!("✓ WSS connection with CA verification successful!");
        }
        Err(e) => {
            warn!("WSS connection with CA verification failed: {}", e);
        }
    }

    // 方案2：跳过证书验证（仅用于测试）
    info!("=== Testing WSS with certificate verification disabled ===");
    match test_wss_without_verification(wss_url).await {
        Ok(_) => {
            info!("✓ WSS connection without verification successful!");
        }
        Err(e) => {
            error!("WSS connection without verification failed: {}", e);
        }
    }

    info!("=== WSS Test Summary ===");
    info!("Both connection methods tested. Check logs above for results.");

    Ok(())
}

async fn test_wss_with_ca(url: &str) -> Result<(), Box<dyn std::error::Error>> {
    // 读取CA证书
    let ca_cert_path = "./certs/ca.crt";
    let ca_cert_data = match std::fs::read(ca_cert_path) {
        Ok(data) => data,
        Err(e) => {
            return Err(format!("Failed to read CA certificate from {}: {}", ca_cert_path, e).into());
        }
    };

    // 创建TLS配置，使用CA证书
    let ca_cert = rustls::Certificate(ca_cert_data);
    let mut root_store = rustls::RootCertStore::empty();
    root_store.add(&ca_cert).map_err(|e| format!("Failed to add CA certificate: {:?}", e))?;

    let config = rustls::ClientConfig::builder()
        .with_safe_defaults()
        .with_root_certificates(root_store)
        .with_no_client_auth();

    let connector = Connector::Rustls(Arc::new(config));

    // 连接到WSS服务器
    let (ws_stream, _) = connect_async_tls_with_config(url, None, false, Some(connector)).await?;
    info!("✓ WSS connection established with CA verification");

    // 测试消息交换
    test_websocket_communication(ws_stream).await?;

    Ok(())
}

async fn test_wss_without_verification(url: &str) -> Result<(), Box<dyn std::error::Error>> {
    // 创建跳过证书验证的TLS配置
    let config = rustls::ClientConfig::builder()
        .with_safe_defaults()
        .with_custom_certificate_verifier(Arc::new(AcceptAnyCertificate))
        .with_no_client_auth();

    let connector = Connector::Rustls(Arc::new(config));

    // 连接到WSS服务器
    let (ws_stream, _) = connect_async_tls_with_config(url, None, false, Some(connector)).await?;
    info!("✓ WSS connection established without certificate verification");

    // 测试消息交换
    test_websocket_communication(ws_stream).await?;

    Ok(())
}

async fn test_websocket_communication(
    ws_stream: tokio_tungstenite::WebSocketStream<tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>>,
) -> Result<(), Box<dyn std::error::Error>> {
    let (mut ws_sender, mut ws_receiver) = ws_stream.split();

    // 发送ping消息
    let ping_msg = json!({
        "type": "Ping"
    });

    ws_sender.send(Message::Text(ping_msg.to_string())).await?;
    info!("✓ Ping message sent over WSS");

    // 等待响应
    let response = timeout(Duration::from_secs(5), ws_receiver.next()).await?;

    match response {
        Some(Ok(Message::Text(text))) => {
            info!("✓ Received response over WSS: {}", text);
        }
        Some(Ok(msg)) => {
            info!("✓ Received message over WSS: {:?}", msg);
        }
        Some(Err(e)) => {
            return Err(format!("WebSocket error: {}", e).into());
        }
        None => {
            return Err("No response received".into());
        }
    }

    // 测试订阅功能
    let subscribe_msg = json!({
        "type": "Subscribe",
        "subscription": "BookTicker"
    });

    ws_sender.send(Message::Text(subscribe_msg.to_string())).await?;
    info!("✓ Subscription message sent over WSS");

    // 等待订阅响应
    let sub_response = timeout(Duration::from_secs(5), ws_receiver.next()).await?;

    match sub_response {
        Some(Ok(Message::Text(text))) => {
            info!("✓ Subscription response received over WSS: {}", text);
        }
        Some(Ok(msg)) => {
            info!("✓ Subscription response received over WSS: {:?}", msg);
        }
        Some(Err(e)) => {
            warn!("WebSocket error during subscription: {}", e);
        }
        None => {
            warn!("No subscription response received");
        }
    }

    // 优雅关闭连接
    ws_sender.send(Message::Close(None)).await?;
    info!("✓ WSS connection closed gracefully");

    Ok(())
}

// 自定义证书验证器，接受任何证书（仅用于测试）
struct AcceptAnyCertificate;

impl rustls::client::ServerCertVerifier for AcceptAnyCertificate {
    fn verify_server_cert(
        &self,
        _end_entity: &rustls::Certificate,
        _intermediates: &[rustls::Certificate],
        _server_name: &rustls::ServerName,
        _scts: &mut dyn Iterator<Item = &[u8]>,
        _ocsp_response: &[u8],
        _now: std::time::SystemTime,
    ) -> Result<rustls::client::ServerCertVerified, rustls::Error> {
        Ok(rustls::client::ServerCertVerified::assertion())
    }
}
