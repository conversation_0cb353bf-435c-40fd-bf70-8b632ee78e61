use futures::{SinkExt, StreamExt};
use rustls::{Certificate, ClientConfig, RootCertStore};
use serde_json::json;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::timeout;
use tokio_tungstenite::{connect_async_tls_with_config, tungstenite::protocol::Message, Connector};
use tracing::{error, info};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("Testing WSS connection with CA certificate verification...");

    // 读取CA证书
    let ca_cert_path = "./certs/ca.crt";
    let ca_cert_data = std::fs::read(ca_cert_path)
        .map_err(|e| format!("Failed to read CA certificate from {}: {}", ca_cert_path, e))?;

    info!("✓ CA certificate loaded from: {}", ca_cert_path);

    // 解析PEM格式的CA证书
    let ca_certs = rustls_pemfile::certs(&mut ca_cert_data.as_slice())
        .map_err(|e| format!("Failed to parse CA certificate PEM: {}", e))?;

    if ca_certs.is_empty() {
        return Err("No certificates found in CA file".into());
    }

    // 创建根证书存储并添加CA证书
    let mut root_store = RootCertStore::empty();
    for cert_der in ca_certs {
        let ca_cert = Certificate(cert_der);
        root_store
            .add(&ca_cert)
            .map_err(|e| format!("Failed to add CA certificate to root store: {:?}", e))?;
    }

    info!("✓ CA certificate added to root certificate store");

    // 创建TLS配置
    let config = ClientConfig::builder()
        .with_safe_defaults()
        .with_root_certificates(root_store)
        .with_no_client_auth();

    let connector = Connector::Rustls(Arc::new(config));

    info!("✓ TLS configuration created with CA verification");

    // 连接到WSS服务器
    let url = "wss://localhost:8082";
    info!("Attempting to connect to: {}", url);

    let (ws_stream, response) = connect_async_tls_with_config(url, None, false, Some(connector))
        .await
        .map_err(|e| format!("Failed to connect to WSS server: {}", e))?;

    info!("✓ WSS connection established successfully!");
    info!("✓ Server response status: {:?}", response.status());

    let (mut ws_sender, mut ws_receiver) = ws_stream.split();

    // 发送ping消息
    let ping_msg = json!({
        "type": "Ping"
    });

    ws_sender.send(Message::Text(ping_msg.to_string())).await?;
    info!("✓ Ping message sent");

    // 等待响应
    match timeout(Duration::from_secs(5), ws_receiver.next()).await? {
        Some(Ok(Message::Text(text))) => {
            info!("✓ Received response: {}", text);
        }
        Some(Ok(msg)) => {
            info!("✓ Received message: {:?}", msg);
        }
        Some(Err(e)) => {
            error!("WebSocket error: {}", e);
            return Err(e.into());
        }
        None => {
            error!("No response received");
            return Err("No response received".into());
        }
    }

    // 发送订阅消息
    let subscribe_msg = json!({
        "type": "Subscribe",
        "subscription": "BookTicker"
    });

    ws_sender
        .send(Message::Text(subscribe_msg.to_string()))
        .await?;
    info!("✓ Subscription message sent");

    // 等待订阅响应
    match timeout(Duration::from_secs(5), ws_receiver.next()).await? {
        Some(Ok(Message::Text(text))) => {
            info!("✓ Subscription response: {}", text);
        }
        Some(Ok(msg)) => {
            info!("✓ Subscription response: {:?}", msg);
        }
        Some(Err(e)) => {
            error!("WebSocket error during subscription: {}", e);
        }
        None => {
            info!("No subscription response received (this is normal)");
        }
    }

    // 优雅关闭连接
    ws_sender.send(Message::Close(None)).await?;
    info!("✓ Connection closed gracefully");

    info!("=== Test Summary ===");
    info!("✓ CA certificate verification successful");
    info!("✓ WSS connection established");
    info!("✓ Message exchange completed");
    info!("✓ All tests passed!");

    Ok(())
}
