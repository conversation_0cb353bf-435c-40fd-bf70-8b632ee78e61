use crate::config::ConfigManager;
use crate::types::{BookTicker, MarketData, Price};
use crate::{BacktestError, Result};
use chrono::{DateTime, Utc};
use std::path::PathBuf;
use std::sync::Arc;
use tokio::fs::File;
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::sync::{mpsc, Mutex};
use tokio::task::JoinHandle;
use tokio::time::Duration;
use tracing::{debug, error, info};

/// 数据读取器状态
#[derive(Debug, Clone, PartialEq)]
pub enum DataReaderStatus {
    /// 空闲状态
    Idle,
    /// 读取中
    Reading,
    /// 暂停状态
    Paused,
    /// 停止状态
    Stopped,
    /// 错误状态
    Error(String),
}

/// 数据读取器控制命令
#[derive(Debug, Clone)]
pub enum ReaderControlCommand {
    /// 开始读取
    Start,
    /// 暂停读取
    Pause,
    /// 恢复读取
    Resume,
    /// 停止读取
    Stop,
}

/// 数据读取器
/// 专注于从文件读取和解析数据，支持精确的状态控制
pub struct DataReader {
    data_path: PathBuf,
    status: Arc<Mutex<DataReaderStatus>>,
    control_rx: Arc<Mutex<mpsc::Receiver<ReaderControlCommand>>>,
    control_tx: mpsc::Sender<ReaderControlCommand>,
    handle: Option<JoinHandle<Result<()>>>,
}

impl DataReader {
    /// 创建新的数据读取器
    pub fn new() -> Result<Self> {
        let config = ConfigManager::get()?;
        let (control_tx, control_rx) = mpsc::channel(32);

        Ok(Self {
            data_path: config.data_paths.root,
            status: Arc::new(Mutex::new(DataReaderStatus::Idle)),
            control_rx: Arc::new(Mutex::new(control_rx)),
            control_tx,
            handle: None,
        })
    }

    /// 从指定路径创建数据读取器
    pub fn with_path(data_path: PathBuf) -> Self {
        let (control_tx, control_rx) = mpsc::channel(32);
        Self {
            data_path,
            status: Arc::new(Mutex::new(DataReaderStatus::Idle)),
            control_rx: Arc::new(Mutex::new(control_rx)),
            control_tx,
            handle: None,
        }
    }

    /// 获取当前状态
    pub async fn get_status(&self) -> DataReaderStatus {
        self.status.lock().await.clone()
    }

    /// 启动读取器
    pub async fn start(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Reading;
        info!("Data reading started");
        Ok(())
    }

    /// 暂停读取器
    pub async fn pause(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Paused;
        info!("Data reading paused");
        Ok(())
    }

    /// 恢复读取器
    pub async fn resume(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Reading;
        info!("Data reading resumed");
        Ok(())
    }

    /// 停止读取器
    pub async fn stop(&self) -> Result<()> {
        *self.status.lock().await = DataReaderStatus::Stopped;
        info!("Data reading stopped");
        Ok(())
    }

    /// 开始读取数据（支持控制命令）
    /// 这是主要的数据读取方法，支持暂停、恢复、停止等控制
    pub async fn prepare_reading(&mut self, output_tx: mpsc::Sender<MarketData>) -> Result<()> {
        info!("Prepare data reading from path: {:?}", self.data_path);

        let config = ConfigManager::get()?;
        let _ = self
            .read_bookticker_data_with_control(output_tx, config.start_time, config.end_time)
            .await;
        info!("Data reading prepared");
        Ok(())
    }

    async fn read_bookticker_data_with_control(
        &mut self,
        output_tx: mpsc::Sender<MarketData>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<()> {
        // 查找CSV格式的BookTicker文件
        let bookticker_files = self.find_bookticker_files().await?;

        if bookticker_files.is_empty() {
            error!("No BookTicker CSV files found in: {:?}", self.data_path);
            return Ok(());
        }

        let mut line_buffer = Vec::new();
        let mut current_line_index = 0;

        // 预加载所有文件的数据
        for file_path in &bookticker_files {
            info!("Loading BookTicker data from: {:?}", file_path);

            let file = File::open(file_path).await.map_err(|e| {
                BacktestError::Data(format!("Failed to open BookTicker file: {}", e))
            })?;

            let reader = BufReader::new(file);
            let mut lines = reader.lines();

            // 跳过CSV头部
            if let Some(_header) = lines
                .next_line()
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to read header: {}", e)))?
            {
                debug!("Skipped CSV header for file: {:?}", file_path);
            }

            // 读取所有行到缓冲区
            while let Some(line) = lines
                .next_line()
                .await
                .map_err(|e| BacktestError::Data(format!("Failed to read line: {}", e)))?
            {
                line_buffer.push(line);
            }
        }

        info!("Loaded {} lines of BookTicker data", line_buffer.len());

        let status = self.status.clone();
        self.handle = Some(tokio::spawn(async move {
            loop {
                let s = status.lock().await.clone();
                match s {
                    DataReaderStatus::Reading => {
                        if current_line_index < line_buffer.len() {
                            let line = &line_buffer[current_line_index];
                            match Self::parse_bookticker_csv_line(line) {
                                Ok(Some(market_data)) => {
                                    if let Err(e) = output_tx.send(market_data).await {
                                        error!("Failed to send BookTicker data: {}", e);
                                        return Err(BacktestError::Data(format!(
                                            "Failed to send data: {}",
                                            e
                                        )));
                                    }
                                }
                                Ok(None) => {
                                    info!("Skipped empty line {}", current_line_index + 1);
                                }
                                Err(e) => {
                                    error!(
                                        "Failed to parse BookTicker line {}: {}",
                                        current_line_index + 1,
                                        e
                                    );
                                }
                            }

                            current_line_index += 1;
                            if current_line_index % 1000000 == 0 {
                                info!(
                                    "BookTicker process: {}/{}",
                                    current_line_index,
                                    line_buffer.len()
                                );
                            }
                        } else {
                            // 所有数据读取完成
                            info!(
                                "All BookTicker data has been read ({} lines)",
                                line_buffer.len()
                            );
                            *status.lock().await = DataReaderStatus::Idle;
                            return Ok(());
                        }
                    }
                    DataReaderStatus::Idle => {
                        tokio::time::sleep(Duration::from_millis(2000)).await;
                    }
                    DataReaderStatus::Stopped => {
                        current_line_index = 0;
                        tokio::time::sleep(Duration::from_millis(2000)).await;
                    }
                    DataReaderStatus::Paused => {
                        tokio::time::sleep(Duration::from_millis(2000)).await;
                    }
                    DataReaderStatus::Error(ref e) => {
                        error!("Data reading error: {}", e);
                        return Err(BacktestError::Data(e.clone()));
                    }
                }
            }
        }));
        Ok(())
    }

    /// 查找BookTicker CSV文件
    async fn find_bookticker_files(&self) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();

        // 获取配置以确定BookTicker数据路径
        let config = ConfigManager::get()?;
        let bookticker_path = config.data_paths.get_bookticker_path();
        info!("Searching for BookTicker files in: {:?}", bookticker_path);

        // 读取BookTicker数据目录
        let mut dir = tokio::fs::read_dir(&bookticker_path).await.map_err(|e| {
            BacktestError::Data(format!("Failed to read BookTicker data directory: {}", e))
        })?;

        while let Some(entry) = dir
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                // 查找包含"bookTicker"的CSV文件
                if file_name.contains("bookTicker") && file_name.ends_with(".csv") {
                    files.push(path);
                }
            }
        }

        // 按文件名排序以确保一致的处理顺序
        files.sort();

        Ok(files)
    }

    /// 查找深度数据CSV文件
    pub async fn find_depth_files(&self) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();

        let config = ConfigManager::get()?;
        let depth_path = config.data_paths.get_depth_path();

        let mut dir = tokio::fs::read_dir(&depth_path).await.map_err(|e| {
            BacktestError::Data(format!("Failed to read depth data directory: {}", e))
        })?;

        while let Some(entry) = dir
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                // 查找包含"depth"的CSV文件
                if file_name.contains("depth") && file_name.ends_with(".csv") {
                    files.push(path);
                }
            }
        }

        files.sort();
        Ok(files)
    }

    /// 查找订单簿数据CSV文件
    pub async fn find_orderbook_files(&self) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();

        let config = ConfigManager::get()?;
        let orderbook_path = config.data_paths.get_orderbook_path();

        let mut dir = tokio::fs::read_dir(&orderbook_path).await.map_err(|e| {
            BacktestError::Data(format!("Failed to read orderbook data directory: {}", e))
        })?;

        while let Some(entry) = dir
            .next_entry()
            .await
            .map_err(|e| BacktestError::Data(format!("Failed to read directory entry: {}", e)))?
        {
            let path = entry.path();
            if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
                // 查找包含"orderbook"的CSV文件
                if file_name.contains("orderbook") && file_name.ends_with(".csv") {
                    files.push(path);
                }
            }
        }

        files.sort();
        Ok(files)
    }

    /// 解析BookTicker CSV行数据
    fn parse_bookticker_csv_line(line: &str) -> Result<Option<MarketData>> {
        if line.trim().is_empty() {
            return Ok(None);
        }

        let fields: Vec<&str> = line.split(',').collect();
        if fields.len() != 7 {
            return Err(BacktestError::Data(format!(
                "Invalid BookTicker CSV format: expected 7 fields, got {}",
                fields.len()
            )));
        }

        // CSV格式: update_id,best_bid_price,best_bid_qty,best_ask_price,best_ask_qty,transaction_time,event_time
        let update_id: u64 = fields[0]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid update_id: {}", e)))?;

        let best_bid_price: f64 = fields[1]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_bid_price: {}", e)))?;

        let best_bid_qty: f64 = fields[2]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_bid_qty: {}", e)))?;

        let best_ask_price: f64 = fields[3]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_ask_price: {}", e)))?;

        let best_ask_qty: f64 = fields[4]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid best_ask_qty: {}", e)))?;

        let transaction_time: u64 = fields[5]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid transaction_time: {}", e)))?;

        let event_time: u64 = fields[6]
            .parse()
            .map_err(|e| BacktestError::Data(format!("Invalid event_time: {}", e)))?;

        let bookticker = BookTicker::new(
            update_id,
            Price::new(best_bid_price),
            best_bid_qty,
            Price::new(best_ask_price),
            best_ask_qty,
            transaction_time,
            event_time,
        );

        Ok(Some(MarketData::BookTicker(bookticker)))
    }
}
