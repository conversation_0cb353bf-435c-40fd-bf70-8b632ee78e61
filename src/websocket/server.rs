use crate::config::ConfigManager;
use crate::websocket::handler::WebSocketHandler;
use crate::websocket::subscription::SubscriptionManager;
use crate::{BacktestError, Result};
use futures::{SinkExt, StreamExt};
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::mpsc;
use tokio_tungstenite::accept_async;
use tracing::{error, info, warn};

/// WebSocket服务器
pub struct WebSocketServer {
    subscription_manager: Arc<SubscriptionManager>,
    port: u16,
}

impl WebSocketServer {
    /// 创建新的WebSocket服务器
    pub fn new() -> Result<Self> {
        let config = ConfigManager::get()?;
        Ok(Self {
            subscription_manager: Arc::new(SubscriptionManager::new()),
            port: config.websocket_port,
        })
    }

    /// 使用指定端口创建WebSocket服务器
    pub fn with_port(port: u16) -> Self {
        Self {
            subscription_manager: Arc::new(SubscriptionManager::new()),
            port,
        }
    }

    /// 启动WebSocket服务器
    pub async fn start(&self) -> Result<()> {
        let config = ConfigManager::get()?;
        let addr = SocketAddr::from(([127, 0, 0, 1], self.port));
        let listener = TcpListener::bind(&addr)
            .await
            .map_err(|e| BacktestError::WebSocket(format!("Failed to bind to {}: {}", addr, e)))?;

        // 检查是否启用TLS
        if config.websocket_tls.enabled {
            match &config.websocket_tls.cert_source {
                Some(crate::config::TlsCertSource::Files {
                    cert_path: _,
                    key_path: _,
                }) => {
                    info!(
                        "WebSocket server listening on: {} (WSS with certificate files)",
                        addr
                    );

                    // 构建TLS配置
                    let tls_config =
                        crate::tls::TlsConfigBuilder::build_server_config(&config.websocket_tls)
                            .await?
                            .ok_or_else(|| {
                                BacktestError::WebSocket("Failed to build TLS config".to_string())
                            })?;

                    let tls_acceptor = tokio_rustls::TlsAcceptor::from(tls_config);

                    while let Ok((stream, client_addr)) = listener.accept().await {
                        info!("New WSS connection from: {}", client_addr);

                        let subscription_manager = Arc::clone(&self.subscription_manager);
                        let acceptor = tls_acceptor.clone();

                        tokio::spawn(async move {
                            match acceptor.accept(stream).await {
                                Ok(tls_stream) => {
                                    if let Err(e) = Self::handle_tls_connection(
                                        tls_stream,
                                        subscription_manager,
                                    )
                                    .await
                                    {
                                        error!(
                                            "Error handling WSS connection from {}: {}",
                                            client_addr, e
                                        );
                                    }
                                }
                                Err(e) => {
                                    let error_msg = e.to_string();
                                    if error_msg.contains("invalid token")
                                        || error_msg.contains("httparse")
                                        || error_msg.contains("InvalidContentType")
                                    {
                                        warn!(
                                            "Client {} attempted plain WebSocket connection to WSS port. Use wss:// instead of ws://",
                                            client_addr
                                        );
                                    } else {
                                        error!("TLS handshake failed for {}: {}", client_addr, e);
                                    }
                                }
                            }
                        });
                    }
                }
                Some(crate::config::TlsCertSource::SelfSigned { .. }) => {
                    warn!("Self-signed certificates require manual certificate generation for WebSocket TLS.");
                    warn!("Falling back to plain WebSocket server on: {}", addr);

                    // 回退到普通WebSocket
                    self.start_plain_websocket(listener, addr).await?;
                }
                None => {
                    warn!("TLS enabled but no certificate source configured. Falling back to plain WebSocket.");
                    self.start_plain_websocket(listener, addr).await?;
                }
            }
        } else {
            info!("WebSocket server listening on: {} (WS)", addr);
            self.start_plain_websocket(listener, addr).await?;
        }

        Ok(())
    }

    /// 启动普通WebSocket服务器
    async fn start_plain_websocket(&self, listener: TcpListener, _addr: SocketAddr) -> Result<()> {
        while let Ok((stream, client_addr)) = listener.accept().await {
            info!("New WebSocket connection from: {}", client_addr);

            let subscription_manager = Arc::clone(&self.subscription_manager);
            tokio::spawn(async move {
                if let Err(e) = Self::handle_connection(stream, subscription_manager).await {
                    error!(
                        "Error handling WebSocket connection from {}: {}",
                        client_addr, e
                    );
                }
            });
        }
        Ok(())
    }

    /// 处理TLS WebSocket连接
    async fn handle_tls_connection(
        tls_stream: tokio_rustls::server::TlsStream<TcpStream>,
        subscription_manager: Arc<SubscriptionManager>,
    ) -> Result<()> {
        let ws_stream = accept_async(tls_stream)
            .await
            .map_err(|e| BacktestError::WebSocket(format!("WebSocket handshake failed: {}", e)))?;

        info!("WSS handshake completed");

        let (ws_sender, mut ws_receiver) = ws_stream.split();
        let (message_tx, mut message_rx) = mpsc::channel::<String>(100);

        // 注册客户端
        let client_id = subscription_manager.add_client(message_tx.clone());
        let mut handler =
            WebSocketHandler::new(client_id, Arc::clone(&subscription_manager), message_tx);

        // 启动消息发送任务
        let mut ws_sender = ws_sender;
        let send_task = tokio::spawn(async move {
            while let Some(message) = message_rx.recv().await {
                if let Err(e) = ws_sender
                    .send(tokio_tungstenite::tungstenite::Message::Text(message))
                    .await
                {
                    error!("Failed to send message: {}", e);
                    break;
                }
            }
        });

        // 处理接收到的消息
        while let Some(msg) = ws_receiver.next().await {
            match msg {
                Ok(message) => {
                    if let Err(e) = handler.handle_message(message).await {
                        error!("Error handling message: {}", e);
                        break;
                    }
                }
                Err(e) => {
                    error!("WebSocket error: {}", e);
                    break;
                }
            }
        }

        // 清理
        send_task.abort();
        subscription_manager.remove_client(&client_id);
        info!("WSS client {} disconnected", client_id);

        Ok(())
    }

    /// 处理WebSocket连接
    async fn handle_connection(
        stream: TcpStream,
        subscription_manager: Arc<SubscriptionManager>,
    ) -> Result<()> {
        let ws_stream = accept_async(stream)
            .await
            .map_err(|e| BacktestError::WebSocket(format!("WebSocket handshake failed: {}", e)))?;

        info!("WebSocket handshake completed");

        let (ws_sender, mut ws_receiver) = ws_stream.split();
        let (message_tx, mut message_rx) = mpsc::channel::<String>(100);

        // 注册客户端
        let client_id = subscription_manager.add_client(message_tx.clone());
        let mut handler =
            WebSocketHandler::new(client_id, Arc::clone(&subscription_manager), message_tx);

        // 启动消息发送任务
        let ws_sender = Arc::new(tokio::sync::Mutex::new(ws_sender));
        let send_task = {
            let ws_sender = Arc::clone(&ws_sender);
            tokio::spawn(async move {
                while let Some(message) = message_rx.recv().await {
                    let mut sender = ws_sender.lock().await;
                    if let Err(e) = sender
                        .send(tokio_tungstenite::tungstenite::Message::Text(message))
                        .await
                    {
                        error!("Failed to send WebSocket message: {}", e);
                        break;
                    }
                }
            })
        };

        // 处理接收到的消息
        let receive_task = tokio::spawn(async move {
            while let Some(message) = ws_receiver.next().await {
                match message {
                    Ok(msg) => {
                        if let Err(e) = handler.handle_message(msg).await {
                            error!("Error handling WebSocket message: {}", e);
                        }
                    }
                    Err(e) => {
                        error!("WebSocket receive error: {}", e);
                        break;
                    }
                }
            }

            // 连接断开，清理客户端
            subscription_manager.remove_client(&client_id);
        });

        // 等待任务完成
        tokio::select! {
            _ = send_task => {
                info!("WebSocket send task completed for client {}", client_id);
            }
            _ = receive_task => {
                info!("WebSocket receive task completed for client {}", client_id);
            }
        }

        Ok(())
    }

    /// 获取订阅管理器
    pub fn subscription_manager(&self) -> Arc<SubscriptionManager> {
        Arc::clone(&self.subscription_manager)
    }

    /// 获取服务器端口
    pub fn port(&self) -> u16 {
        self.port
    }

    /// 获取连接的客户端数量
    pub fn client_count(&self) -> usize {
        self.subscription_manager.client_count()
    }
}

impl Default for WebSocketServer {
    fn default() -> Self {
        Self::with_port(8080)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{sleep, Duration};

    #[tokio::test]
    async fn test_websocket_server_creation() {
        let server = WebSocketServer::with_port(8888);
        assert_eq!(server.port(), 8888);
        assert_eq!(server.client_count(), 0);
    }

    #[tokio::test]
    async fn test_websocket_server_start() {
        let _server = WebSocketServer::with_port(0); // 使用随机端口

        // 启动服务器（在后台）
        let server_task = tokio::spawn(async move {
            // 这个测试只是验证服务器可以启动，不会实际运行
            // 在实际测试中，你可能需要更复杂的设置
        });

        // 等待一小段时间
        sleep(Duration::from_millis(100)).await;

        // 取消任务
        server_task.abort();
    }
}
