use thiserror::Error;

#[derive(Erro<PERSON>, Debug)]
pub enum BacktestError {
    #[error("Configuration error: {0}")]
    Config(String),
    
    #[error("Data error: {0}")]
    Data(String),
    
    #[error("Matching engine error: {0}")]
    Matching(String),
    
    #[error("WebSocket error: {0}")]
    WebSocket(String),
    
    #[error("HTTP error: {0}")]
    Http(String),
    
    #[error("Communication error: {0}")]
    Communication(String),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    
    #[error("Serialization error: {0}")]
    Serde(#[from] serde_json::Error),
    
    #[error("Time error: {0}")]
    Time(String),
}

pub type Result<T> = std::result::Result<T, BacktestError>;
