// Rust客户端TLS配置示例

use native_tls::{TlsConnector, Certificate};
use std::fs;

fn create_tls_connector() -> Result<TlsConnector, Box<dyn std::error::Error>> {
    // 方案1: 使用CA证书
    let ca_cert_data = fs::read("./certs/ca.crt")?;
    let ca_cert = Certificate::from_pem(&ca_cert_data)?;
    
    let connector = TlsConnector::builder()
        .add_root_certificate(ca_cert)
        .build()?;
    
    Ok(connector)
}

fn create_insecure_connector() -> Result<TlsConnector, Box<dyn std::error::Error>> {
    // 方案2: 跳过证书验证（仅用于开发测试）
    let connector = TlsConnector::builder()
        .danger_accept_invalid_certs(true)
        .danger_accept_invalid_hostnames(true)
        .build()?;
    
    Ok(connector)
}
