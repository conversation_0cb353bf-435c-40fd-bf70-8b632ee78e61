// Node.js客户端TLS配置示例

const WebSocket = require('ws');
const fs = require('fs');
const https = require('https');

// 方案1: 使用CA证书
function createSecureWebSocket() {
    const caCert = fs.readFileSync('./certs/ca.crt');
    
    const agent = new https.Agent({
        ca: caCert
    });
    
    const ws = new WebSocket('wss://localhost:8082', {
        agent: agent
    });
    
    return ws;
}

// 方案2: 跳过证书验证（仅用于开发测试）
function createInsecureWebSocket() {
    process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
    
    const ws = new WebSocket('wss://localhost:8082');
    
    return ws;
}
