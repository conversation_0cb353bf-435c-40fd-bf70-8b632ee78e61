# Python客户端TLS配置示例

import ssl
import websocket

def create_ssl_context():
    """创建SSL上下文，使用CA证书"""
    ssl_context = ssl.create_default_context()
    ssl_context.load_verify_locations('./certs/ca.crt')
    return ssl_context

def create_insecure_ssl_context():
    """创建不验证证书的SSL上下文（仅用于开发测试）"""
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    return ssl_context

# 使用示例
if __name__ == "__main__":
    # 方案1: 使用CA证书
    ssl_context = create_ssl_context()
    ws = websocket.WebSocket(sslopt={"context": ssl_context})
    
    # 方案2: 跳过证书验证
    # ssl_context = create_insecure_ssl_context()
    # ws = websocket.WebSocket(sslopt={"context": ssl_context})
