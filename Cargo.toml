[package]
name = "backtest"
version = "0.1.0"
edition = "2021"

[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# WebSocket支持
tokio-tungstenite = "0.20"

# HTTP服务器
warp = "0.3"

# 日志系统
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 配置管理
once_cell = "1.19"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 数据结构
dashmap = "5.5"

# UUID生成
uuid = { version = "1.0", features = ["v4", "serde"] }

# 异步流处理
futures = "0.3"

# 命令行参数解析
clap = { version = "4.0", features = ["derive"] }

# 交互式命令行
rustyline = "13.0"

# 彩色输出
colored = "2.0"

[dev-dependencies]
tokio-test = "0.4"

[[bin]]
name = "backtest"
path = "src/main.rs"

[[bin]]
name = "websocket_client"
path = "tools/websocket_client.rs"
