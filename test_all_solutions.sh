#!/bin/bash

# 测试所有TLS解决方案的脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  测试所有TLS解决方案${NC}"
echo -e "${BLUE}========================================${NC}"
echo

# 启动服务器
start_server() {
    echo -e "${YELLOW}1. 启动TLS服务器...${NC}"
    
    # 检查服务器是否已经在运行
    if pgrep -f "backtest.*--config.*test_tls_config.toml" > /dev/null; then
        echo -e "${GREEN}✓ 服务器已在运行${NC}"
        return 0
    fi
    
    # 启动服务器
    cargo run --bin backtest -- --config test_tls_config.toml > server.log 2>&1 &
    SERVER_PID=$!
    
    # 等待服务器启动
    echo -e "${BLUE}等待服务器启动...${NC}"
    sleep 5
    
    # 检查服务器是否成功启动
    if kill -0 $SERVER_PID 2>/dev/null; then
        echo -e "${GREEN}✓ 服务器启动成功 (PID: $SERVER_PID)${NC}"
        echo "SERVER_PID=$SERVER_PID" > .server_pid
    else
        echo -e "${RED}❌ 服务器启动失败${NC}"
        cat server.log
        exit 1
    fi
    echo
}

# 测试HTTPS连接
test_https() {
    echo -e "${YELLOW}2. 测试HTTPS连接...${NC}"
    
    # 使用CA证书测试
    if curl -s --cacert ./certs/ca.crt https://localhost:8083/api/v1/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ HTTPS连接成功（使用CA证书）${NC}"
    else
        echo -e "${YELLOW}⚠ HTTPS连接失败（可能端口冲突）${NC}"
    fi
    
    # 使用证书包测试
    if curl -s --cacert ./certs/ca-bundle.crt https://localhost:8083/api/v1/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ HTTPS连接成功（使用证书包）${NC}"
    else
        echo -e "${YELLOW}⚠ HTTPS连接失败（使用证书包）${NC}"
    fi
    
    # 跳过证书验证测试
    if curl -s -k https://localhost:8083/api/v1/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ HTTPS连接成功（跳过证书验证）${NC}"
    else
        echo -e "${YELLOW}⚠ HTTPS连接失败（跳过证书验证）${NC}"
    fi
    echo
}

# 测试WSS连接
test_wss() {
    echo -e "${YELLOW}3. 测试WSS连接...${NC}"
    
    # 使用CA证书测试
    if RUST_LOG=error cargo run --bin test_ca_client > /dev/null 2>&1; then
        echo -e "${GREEN}✓ WSS连接成功（CA证书验证）${NC}"
    else
        echo -e "${RED}❌ WSS连接失败（CA证书验证）${NC}"
    fi
    
    # 使用跳过验证测试
    if RUST_LOG=error cargo run --bin test_wss > /dev/null 2>&1; then
        echo -e "${GREEN}✓ WSS连接成功（跳过证书验证）${NC}"
    else
        echo -e "${YELLOW}⚠ WSS连接失败（跳过证书验证）${NC}"
    fi
    echo
}

# 测试环境变量
test_environment() {
    echo -e "${YELLOW}4. 测试环境变量...${NC}"
    
    # 设置环境变量
    source ./certs/setup_env.sh > /dev/null 2>&1
    
    # 检查环境变量是否设置
    if [[ -n "$SSL_CERT_FILE" ]]; then
        echo -e "${GREEN}✓ SSL_CERT_FILE 已设置: $SSL_CERT_FILE${NC}"
    else
        echo -e "${RED}❌ SSL_CERT_FILE 未设置${NC}"
    fi
    
    if [[ -n "$SSL_CERT_DIR" ]]; then
        echo -e "${GREEN}✓ SSL_CERT_DIR 已设置: $SSL_CERT_DIR${NC}"
    else
        echo -e "${RED}❌ SSL_CERT_DIR 未设置${NC}"
    fi
    
    # 测试使用环境变量的curl
    if curl -s https://localhost:8083/api/v1/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 使用环境变量的HTTPS连接成功${NC}"
    else
        echo -e "${YELLOW}⚠ 使用环境变量的HTTPS连接失败${NC}"
    fi
    echo
}

# 显示客户端配置示例
show_client_examples() {
    echo -e "${YELLOW}5. 客户端配置示例...${NC}"
    
    echo -e "${BLUE}Rust客户端示例:${NC}"
    echo -e "${GREEN}// 方案1: 使用CA证书${NC}"
    echo -e "let ca_cert_data = std::fs::read(\"./certs/ca.crt\")?;"
    echo -e "let ca_cert = Certificate::from_pem(&ca_cert_data)?;"
    echo -e "let connector = TlsConnector::builder()"
    echo -e "    .add_root_certificate(ca_cert)"
    echo -e "    .build()?;"
    echo
    
    echo -e "${GREEN}// 方案2: 跳过证书验证（仅开发测试）${NC}"
    echo -e "let connector = TlsConnector::builder()"
    echo -e "    .danger_accept_invalid_certs(true)"
    echo -e "    .danger_accept_invalid_hostnames(true)"
    echo -e "    .build()?;"
    echo
    
    echo -e "${BLUE}Python客户端示例:${NC}"
    echo -e "${GREEN}# 方案1: 使用CA证书${NC}"
    echo -e "ssl_context = ssl.create_default_context()"
    echo -e "ssl_context.load_verify_locations('./certs/ca.crt')"
    echo -e "ws = websocket.WebSocket(sslopt={\"context\": ssl_context})"
    echo
    
    echo -e "${GREEN}# 方案2: 跳过证书验证（仅开发测试）${NC}"
    echo -e "ssl_context = ssl.create_default_context()"
    echo -e "ssl_context.check_hostname = False"
    echo -e "ssl_context.verify_mode = ssl.CERT_NONE"
    echo -e "ws = websocket.WebSocket(sslopt={\"context\": ssl_context})"
    echo
}

# 清理函数
cleanup() {
    echo -e "${YELLOW}清理资源...${NC}"
    
    # 停止服务器
    if [[ -f ".server_pid" ]]; then
        SERVER_PID=$(cat .server_pid | cut -d'=' -f2)
        if kill -0 $SERVER_PID 2>/dev/null; then
            kill $SERVER_PID
            echo -e "${GREEN}✓ 服务器已停止${NC}"
        fi
        rm -f .server_pid
    fi
    
    # 清理日志文件
    rm -f server.log
}

# 显示总结
show_summary() {
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}  测试完成！解决方案总结${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo
    echo -e "${YELLOW}针对你的错误 'unable to get local issuer certificate'：${NC}"
    echo
    echo -e "${GREEN}推荐解决方案（按优先级排序）：${NC}"
    echo
    echo -e "${BLUE}1. 在客户端代码中添加CA证书（推荐）${NC}"
    echo -e "   - 使用 ./certs/ca.crt"
    echo -e "   - 参考 ./certs/rust_client_example.rs"
    echo -e "   - 参考 ./certs/python_client_example.py"
    echo
    echo -e "${BLUE}2. 设置环境变量${NC}"
    echo -e "   - 运行: source ./certs/setup_env.sh"
    echo -e "   - 适用于支持环境变量的客户端"
    echo
    echo -e "${BLUE}3. 系统级别安装CA证书${NC}"
    echo -e "   - 运行: sudo ./fix_tls_certificates.sh"
    echo -e "   - 影响整个系统"
    echo
    echo -e "${BLUE}4. 临时跳过证书验证（仅开发测试）${NC}"
    echo -e "   - 在客户端代码中禁用证书验证"
    echo -e "   - 不推荐用于生产环境"
    echo
    echo -e "${YELLOW}测试命令：${NC}"
    echo -e "  - 测试HTTPS: curl --cacert ./certs/ca.crt https://localhost:8083/api/v1/health"
    echo -e "  - 测试WSS: cargo run --bin test_ca_client"
    echo -e "  - 重新运行此测试: ./test_all_solutions.sh"
    echo
}

# 主函数
main() {
    # 设置清理陷阱
    trap cleanup EXIT
    
    start_server
    test_https
    test_wss
    test_environment
    show_client_examples
    show_summary
}

# 运行主函数
main
