use clap::{Arg, Command};
use futures::{SinkExt, StreamExt};
use serde_json::{json, Value};
use std::time::Duration;
use tokio::time::{interval, timeout};
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let matches = Command::new("WebSocket Client Tool")
        .version("1.0")
        .about("WebSocket client tool for testing backtest framework")
        .arg(
            Arg::new("host")
                .long("host")
                .value_name("HOST")
                .help("Server host")
                .default_value("127.0.0.1"),
        )
        .arg(
            Arg::new("port")
                .short('p')
                .long("port")
                .value_name("PORT")
                .help("Server port")
                .default_value("8080"),
        )
        .arg(
            Arg::new("mode")
                .short('m')
                .long("mode")
                .value_name("MODE")
                .help("Test mode")
                .required(true)
                .value_parser([
                    "connect",
                    "subscribe",
                    "binance",
                    "interactive",
                    "stress",
                    "ping",
                ]),
        )
        .arg(
            Arg::new("subscriptions")
                .short('s')
                .long("subscriptions")
                .value_name("SUBSCRIPTIONS")
                .help("Subscription types to subscribe to (comma-separated)")
                .default_value("OrderBook,Trade,Bbo,BookTicker"),
        )
        .arg(
            Arg::new("duration")
                .short('d')
                .long("duration")
                .value_name("SECONDS")
                .help("Test duration in seconds")
                .default_value("30"),
        )
        .arg(
            Arg::new("verbose")
                .short('v')
                .long("verbose")
                .help("Verbose output")
                .action(clap::ArgAction::SetTrue),
        )
        .get_matches();

    let host = matches.get_one::<String>("host").unwrap();
    let port = matches.get_one::<String>("port").unwrap();
    let mode = matches.get_one::<String>("mode").unwrap();
    let subscriptions = matches.get_one::<String>("subscriptions").unwrap();
    let duration: u64 = matches.get_one::<String>("duration").unwrap().parse()?;
    let verbose = matches.get_flag("verbose");

    let url = format!("ws://{}:{}", host, port);

    match mode.as_str() {
        "connect" => test_connection(&url, verbose).await?,
        "subscribe" => test_subscription(&url, subscriptions, duration, verbose).await?,
        "binance" => test_binance_subscription(&url, subscriptions, duration, verbose).await?,
        "interactive" => interactive_mode(&url, verbose).await?,
        "stress" => stress_test(&url, duration, verbose).await?,
        "ping" => ping_test(&url, duration, verbose).await?,
        _ => return Err("Unknown mode".into()),
    }

    Ok(())
}

async fn test_connection(url: &str, verbose: bool) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔗 Testing WebSocket connection to {}", url);

    let start = std::time::Instant::now();
    let (ws_stream, response) = connect_async(url).await?;
    let duration = start.elapsed();

    println!("✅ Connected successfully in {:?}", duration);

    if verbose {
        println!("📋 Response status: {}", response.status());
        println!("📋 Response headers:");
        for (name, value) in response.headers() {
            println!("   {}: {:?}", name, value);
        }
    }

    let (mut write, mut read) = ws_stream.split();

    // Send a simple ping
    write.send(Message::Ping(vec![])).await?;
    println!("📤 Sent ping");

    // Wait for pong
    if let Some(msg) = timeout(Duration::from_secs(5), read.next()).await? {
        match msg? {
            Message::Pong(_) => println!("📥 Received pong"),
            other => println!("📥 Received: {:?}", other),
        }
    }

    println!("🔌 Closing connection");
    write.close().await?;

    Ok(())
}

async fn test_subscription(
    url: &str,
    subscriptions: &str,
    duration: u64,
    verbose: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("📡 Testing WebSocket subscription to {}", url);
    println!("📋 Subscriptions: {}", subscriptions);
    println!("⏱️  Duration: {} seconds", duration);

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    // Subscribe to subscription types
    let subscription_list: Vec<&str> = subscriptions.split(',').collect();
    for subscription_type in subscription_list {
        let subscribe_msg = json!({
            "type": "Subscribe",
            "subscription": subscription_type.trim()
        });

        write.send(Message::Text(subscribe_msg.to_string())).await?;
        println!("📤 Sent subscription: {}", subscribe_msg);
    }

    let mut message_count = 0;
    let start_time = std::time::Instant::now();

    while start_time.elapsed().as_secs() < duration {
        if let Ok(Some(msg)) = timeout(Duration::from_secs(1), read.next()).await {
            match msg? {
                Message::Text(text) => {
                    message_count += 1;
                    if verbose {
                        println!("📥 Message {}: {}", message_count, format_message(&text));
                    } else {
                        if message_count % 10 == 0 {
                            println!("📥 Received {} messages", message_count);
                        }
                    }
                }
                Message::Ping(_) => {
                    write.send(Message::Pong(vec![])).await?;
                    if verbose {
                        println!("🏓 Ping/Pong");
                    }
                }
                other => {
                    if verbose {
                        println!("📥 Other message: {:?}", other);
                    }
                }
            }
        }
    }

    println!("📊 Total messages received: {}", message_count);
    println!(
        "📊 Messages per second: {:.2}",
        message_count as f64 / duration as f64
    );

    write.close().await?;
    Ok(())
}

async fn test_binance_subscription(
    url: &str,
    subscriptions: &str,
    duration: u64,
    verbose: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("📡 Testing Binance-style WebSocket subscription to {}", url);
    println!("📋 Streams: {}", subscriptions);
    println!("⏱️  Duration: {} seconds", duration);

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    // Subscribe using Binance format
    let stream_list: Vec<&str> = subscriptions.split(',').collect();
    let subscribe_msg = json!({
        "method": "SUBSCRIBE",
        "params": stream_list,
        "id": 1
    });

    write.send(Message::Text(subscribe_msg.to_string())).await?;
    println!("📤 Sent Binance subscription: {}", subscribe_msg);

    let mut message_count = 0;
    let start_time = std::time::Instant::now();

    while start_time.elapsed().as_secs() < duration {
        if let Ok(Some(msg)) = timeout(Duration::from_secs(1), read.next()).await {
            match msg? {
                Message::Text(text) => {
                    message_count += 1;
                    if verbose {
                        println!("📥 Message {}: {}", message_count, text);
                    } else {
                        // 尝试解析为Binance格式
                        if let Ok(data) = serde_json::from_str::<Value>(&text) {
                            if let Some(event_type) = data.get("e").and_then(|v| v.as_str()) {
                                match event_type {
                                    "bookTicker" => {
                                        let formatted = format_binance_book_ticker(&data);
                                        println!("📥 Message {}: {}", message_count, formatted);
                                    }
                                    "depthUpdate" => {
                                        let formatted = format_binance_depth_update(&data);
                                        println!("📥 Message {}: {}", message_count, formatted);
                                    }
                                    _ => {
                                        println!("📥 Message {}: {}", message_count, text);
                                    }
                                }
                            } else {
                                println!("📥 Message {}: {}", message_count, text);
                            }
                        } else {
                            println!("📥 Message {}: {}", message_count, text);
                        }
                    }
                }
                Message::Ping(payload) => {
                    write.send(Message::Pong(payload)).await?;
                    if verbose {
                        println!("🏓 Ping/Pong");
                    }
                }
                _ => {}
            }
        }
    }

    println!("📊 Total messages received: {}", message_count);
    println!(
        "📊 Messages per second: {:.2}",
        message_count as f64 / duration as f64
    );

    write.close().await?;
    Ok(())
}

async fn interactive_mode(url: &str, verbose: bool) -> Result<(), Box<dyn std::error::Error>> {
    println!("🎮 Interactive WebSocket mode");
    println!("💡 Commands:");
    println!("   subscribe <subscriptions>  - Subscribe to subscription types (OrderBook,Trade,Bbo,BookTicker)");
    println!("   unsubscribe <subscriptions> - Unsubscribe from subscription types");
    println!("   ping                       - Send ping");
    println!("   quit                       - Exit");

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    // Spawn task to handle incoming messages
    let read_task = tokio::spawn(async move {
        while let Some(msg) = read.next().await {
            match msg {
                Ok(Message::Text(text)) => println!("📥 Received: {}", format_message(&text)),
                Ok(Message::Ping(_)) => println!("🏓 Received ping"),
                Ok(Message::Pong(_)) => println!("🏓 Received pong"),
                Ok(other) => {
                    if verbose {
                        println!("📥 Other: {:?}", other);
                    }
                }
                Err(e) => {
                    println!("❌ Error: {}", e);
                    break;
                }
            }
        }
    });

    // Handle user input
    loop {
        println!("\n> ");
        let mut input = String::new();
        std::io::stdin().read_line(&mut input)?;
        let input = input.trim();

        if input.is_empty() {
            continue;
        }

        let parts: Vec<&str> = input.split_whitespace().collect();
        match parts[0] {
            "quit" => break,
            "ping" => {
                write.send(Message::Ping(vec![])).await?;
                println!("📤 Sent ping");
            }
            "subscribe" => {
                if parts.len() > 1 {
                    let subscriptions: Vec<&str> = parts[1].split(',').collect();
                    for subscription in subscriptions {
                        let msg = json!({
                            "type": "Subscribe",
                            "subscription": subscription.trim()
                        });
                        write.send(Message::Text(msg.to_string())).await?;
                        println!("📤 Sent: {}", msg);
                    }
                } else {
                    println!("❌ Usage: subscribe <subscriptions>");
                }
            }
            "unsubscribe" => {
                if parts.len() > 1 {
                    let subscriptions: Vec<&str> = parts[1].split(',').collect();
                    for subscription in subscriptions {
                        let msg = json!({
                            "type": "Unsubscribe",
                            "subscription": subscription.trim()
                        });
                        write.send(Message::Text(msg.to_string())).await?;
                        println!("📤 Sent: {}", msg);
                    }
                } else {
                    println!("❌ Usage: unsubscribe <subscriptions>");
                }
            }
            _ => {
                // Send as raw message
                write.send(Message::Text(input.to_string())).await?;
                println!("📤 Sent: {}", input);
            }
        }
    }

    read_task.abort();
    write.close().await?;
    Ok(())
}

async fn stress_test(
    url: &str,
    duration: u64,
    verbose: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("🔥 WebSocket stress test for {} seconds", duration);

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    let mut sent_count = 0;
    let mut received_count = 0;
    let start_time = std::time::Instant::now();

    // Spawn message sender
    let write_task = tokio::spawn(async move {
        let mut interval = interval(Duration::from_millis(100));
        let mut counter = 0;

        while start_time.elapsed().as_secs() < duration {
            interval.tick().await;
            counter += 1;

            let msg = json!({
                "type": "test_message",
                "id": counter,
                "timestamp": chrono::Utc::now().to_rfc3339()
            });

            if write.send(Message::Text(msg.to_string())).await.is_err() {
                break;
            }

            sent_count += 1;
        }

        sent_count
    });

    // Handle incoming messages
    while start_time.elapsed().as_secs() < duration {
        if let Ok(Some(msg)) = timeout(Duration::from_millis(100), read.next()).await {
            match msg? {
                Message::Text(_) => {
                    received_count += 1;
                    if verbose && received_count % 50 == 0 {
                        println!("📊 Sent: {}, Received: {}", sent_count, received_count);
                    }
                }
                _ => {}
            }
        }
    }

    let final_sent = write_task.await?;

    println!("📊 Stress test results:");
    println!("   Messages sent: {}", final_sent);
    println!("   Messages received: {}", received_count);
    println!(
        "   Send rate: {:.2} msg/s",
        final_sent as f64 / duration as f64
    );
    println!(
        "   Receive rate: {:.2} msg/s",
        received_count as f64 / duration as f64
    );

    Ok(())
}

async fn ping_test(
    url: &str,
    duration: u64,
    verbose: bool,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("🏓 WebSocket ping test for {} seconds", duration);

    let (ws_stream, _) = connect_async(url).await?;
    let (mut write, mut read) = ws_stream.split();

    let mut ping_count = 0;
    let mut pong_count = 0;
    let mut total_latency = Duration::new(0, 0);
    let start_time = std::time::Instant::now();

    let mut interval = interval(Duration::from_secs(1));

    while start_time.elapsed().as_secs() < duration {
        interval.tick().await;

        let ping_time = std::time::Instant::now();
        write.send(Message::Ping(vec![])).await?;
        ping_count += 1;

        if verbose {
            println!("📤 Ping #{}", ping_count);
        }

        // Wait for pong
        if let Ok(Some(msg)) = timeout(Duration::from_secs(2), read.next()).await {
            match msg? {
                Message::Pong(_) => {
                    let latency = ping_time.elapsed();
                    total_latency += latency;
                    pong_count += 1;

                    if verbose {
                        println!("📥 Pong #{} (latency: {:?})", pong_count, latency);
                    }
                }
                _ => {}
            }
        }
    }

    println!("📊 Ping test results:");
    println!("   Pings sent: {}", ping_count);
    println!("   Pongs received: {}", pong_count);
    println!(
        "   Success rate: {:.1}%",
        (pong_count as f64 / ping_count as f64) * 100.0
    );

    if pong_count > 0 {
        let avg_latency = total_latency / pong_count as u32;
        println!("   Average latency: {:?}", avg_latency);
    }

    write.close().await?;
    Ok(())
}

/// 格式化WebSocket消息以便更好地显示
fn format_message(text: &str) -> String {
    match serde_json::from_str::<Value>(text) {
        Ok(json) => {
            if let Some(msg_type) = json.get("type").and_then(|v| v.as_str()) {
                match msg_type {
                    "Data" => {
                        if let (Some(subscription), Some(data)) = (
                            json.get("subscription").and_then(|v| v.as_str()),
                            json.get("data"),
                        ) {
                            format_data_message(subscription, data)
                        } else {
                            text.to_string()
                        }
                    }
                    "Subscribed" => {
                        if let Some(subscription) =
                            json.get("subscription").and_then(|v| v.as_str())
                        {
                            format!("✅ Subscribed to {}", subscription)
                        } else {
                            text.to_string()
                        }
                    }
                    "Unsubscribed" => {
                        if let Some(subscription) =
                            json.get("subscription").and_then(|v| v.as_str())
                        {
                            format!("❌ Unsubscribed from {}", subscription)
                        } else {
                            text.to_string()
                        }
                    }
                    "Error" => {
                        if let Some(message) = json.get("message").and_then(|v| v.as_str()) {
                            format!("🚨 Error: {}", message)
                        } else {
                            text.to_string()
                        }
                    }
                    _ => text.to_string(),
                }
            } else {
                text.to_string()
            }
        }
        Err(_) => text.to_string(),
    }
}

/// 格式化Binance BookTicker消息
fn format_binance_book_ticker(data: &Value) -> String {
    if let (Some(symbol), Some(bid_price), Some(bid_qty), Some(ask_price), Some(ask_qty)) = (
        data.get("s").and_then(|v| v.as_str()),
        data.get("b")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok()),
        data.get("B")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok()),
        data.get("a")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok()),
        data.get("A")
            .and_then(|v| v.as_str())
            .and_then(|s| s.parse::<f64>().ok()),
    ) {
        format!(
            "📊 BookTicker {}: Bid {:.8}@{:.8} | Ask {:.8}@{:.8} | Spread: {:.8}",
            symbol,
            bid_price,
            bid_qty,
            ask_price,
            ask_qty,
            ask_price - bid_price
        )
    } else {
        format!("📊 BookTicker: {}", data)
    }
}

/// 格式化Binance Depth Update消息
fn format_binance_depth_update(data: &Value) -> String {
    if let (Some(symbol), Some(bids), Some(asks)) = (
        data.get("s").and_then(|v| v.as_str()),
        data.get("b").and_then(|v| v.as_array()),
        data.get("a").and_then(|v| v.as_array()),
    ) {
        let bid_count = bids.len();
        let ask_count = asks.len();

        let best_bid = if let Some(bid) = bids.first() {
            if let Some(bid_array) = bid.as_array() {
                if bid_array.len() >= 2 {
                    format!(
                        "{}@{}",
                        bid_array[0].as_str().unwrap_or("0"),
                        bid_array[1].as_str().unwrap_or("0")
                    )
                } else {
                    "N/A".to_string()
                }
            } else {
                "N/A".to_string()
            }
        } else {
            "N/A".to_string()
        };

        let best_ask = if let Some(ask) = asks.first() {
            if let Some(ask_array) = ask.as_array() {
                if ask_array.len() >= 2 {
                    format!(
                        "{}@{}",
                        ask_array[0].as_str().unwrap_or("0"),
                        ask_array[1].as_str().unwrap_or("0")
                    )
                } else {
                    "N/A".to_string()
                }
            } else {
                "N/A".to_string()
            }
        } else {
            "N/A".to_string()
        };

        format!(
            "📈 Depth {} | Bids: {} | Asks: {} | Best: {} / {}",
            symbol, bid_count, ask_count, best_bid, best_ask
        )
    } else {
        format!("📈 Depth: {}", data)
    }
}

/// 格式化数据消息
fn format_data_message(subscription: &str, data: &Value) -> String {
    match subscription {
        "BookTicker" => {
            if let (Some(bid_price), Some(bid_qty), Some(ask_price), Some(ask_qty)) = (
                data.get("best_bid_price").and_then(|v| v.as_f64()),
                data.get("best_bid_qty").and_then(|v| v.as_f64()),
                data.get("best_ask_price").and_then(|v| v.as_f64()),
                data.get("best_ask_qty").and_then(|v| v.as_f64()),
            ) {
                format!(
                    "📊 BookTicker: Bid {:.2}@{:.2} | Ask {:.2}@{:.2} | Spread: {:.4}",
                    bid_price,
                    bid_qty,
                    ask_price,
                    ask_qty,
                    ask_price - bid_price
                )
            } else {
                format!("📊 BookTicker: {}", data)
            }
        }
        "Bbo" => {
            if let (Some(bid_price), Some(bid_qty), Some(ask_price), Some(ask_qty)) = (
                data.get("bid_price").and_then(|v| v.as_f64()),
                data.get("bid_qty").and_then(|v| v.as_f64()),
                data.get("ask_price").and_then(|v| v.as_f64()),
                data.get("ask_qty").and_then(|v| v.as_f64()),
            ) {
                format!(
                    "📈 BBO: Bid {:.2}@{:.2} | Ask {:.2}@{:.2}",
                    bid_price, bid_qty, ask_price, ask_qty
                )
            } else {
                format!("📈 BBO: {}", data)
            }
        }
        "Trade" => {
            if let (Some(price), Some(quantity)) = (
                data.get("price").and_then(|v| v.as_f64()),
                data.get("quantity").and_then(|v| v.as_f64()),
            ) {
                let side = data
                    .get("side")
                    .and_then(|v| v.as_str())
                    .unwrap_or("Unknown");
                format!("💰 Trade: {} {:.2}@{:.2}", side, quantity, price)
            } else {
                format!("💰 Trade: {}", data)
            }
        }
        "OrderBook" => {
            format!("📚 OrderBook update")
        }
        _ => format!("{}: {}", subscription, data),
    }
}
